import { TableType } from "@/types/tables";
import { classifyActivitySchema, classifyContractSchema, classifyFarmerSchema } from "@acr/common/schemas/classify";

export const classifyRows = (rows: unknown[]): TableType[] => {
  const suggestions: TableType[] = [];

  if (rows.every(r => classifyFarmerSchema.safeParse(r).success)) {
    suggestions.push("t_farmer");
  }
  if (rows.every(r => classifyActivitySchema.safeParse(r).success)) {
    suggestions.push("t_activity");
  }
  if (rows.every(r => classifyContractSchema.safeParse(r).success)) {
    suggestions.push("t_payment_contract");
  }

  return suggestions.length ? suggestions : [];
};
