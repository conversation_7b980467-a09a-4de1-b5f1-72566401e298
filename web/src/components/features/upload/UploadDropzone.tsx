import { UploadIcon } from "lucide-react";
import { Dropzone, DropzoneEmptyState, DropzoneContent } from "../../ui/shadcn-io/dropzone";
import { useState } from "react";
import { parseFile } from "@/utils/parse";
import { acceptedFileTypes } from "@/utils/constants";
import { toast } from "sonner";
import { Skeleton } from "@/components/ui/skeleton";
import { TypographyP } from "@/components/ui/typography";

export const UploadDropzone = () => {
  const [files, setFiles] = useState<File[] | undefined>();
  const [isLoading, setIsLoading] = useState(false);

  const handleDrop = async (files: File[]) => {
    setIsLoading(true);
    console.log(files);
    setFiles(files);

    const parsed = await Promise.all(files.map(parseFile));
    console.log(parsed);

    setIsLoading(false);
  };

  const handleError = (error: Error) => {
    toast('File type invalid', {
      description: error.message
    });
  };

  return (
    <Dropzone onDrop={handleDrop} onError={handleError} src={files} className="cursor-pointer" accept={acceptedFileTypes}>
      <DropzoneEmptyState>
          <div className="flex w-full items-center gap-4 p-8">
            <div className="flex size-16 items-center justify-center rounded-lg bg-muted text-muted-foreground">
              <UploadIcon size={24} />
            </div>
            <div className="text-left">
              <p className="font-medium text-sm">Upload a file</p>
              <p className="text-muted-foreground text-xs">
                Drag and drop or click to upload
              </p>
            </div>
          </div>

      </DropzoneEmptyState>
      <DropzoneContent>
        {isLoading ? (
          <>
            <TypographyP>
              Uploading
            </TypographyP>
            <Skeleton className="h-[20px] w-full rounded-full" />
          </>
         ) : null}
      </DropzoneContent>
    </Dropzone>
  );
};
